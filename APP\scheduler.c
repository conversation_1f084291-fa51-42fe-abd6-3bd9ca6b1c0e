#include "scheduler.h"

/* 变量定义 */
uint8_t task_num;

/* 调度器相关 */
typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

static task_t scheduler_task[] =
{
    {lcd_task, 1, 0},
    {Key_task, 10, 0},
    {rfid_task, 5, 0},
    {AS608_task, 5, 0},
    {esp8266_task, 10, 0}
};

void scheduler_init(void)
{
    Tim2_Init(1000, 96-1);
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
		uint32_t now_time = uwTick;
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}



